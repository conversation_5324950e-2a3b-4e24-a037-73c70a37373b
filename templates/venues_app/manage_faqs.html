{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}
{% load widget_tweaks %}

{% block title %}Manage FAQs - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&family=Poppins:wght@400;500;600&display=swap');

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        font-size: 1.375rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0;
        border-bottom: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-section-title i {
        color: #fae1d7;
        font-size: 2.5rem;
        width: 3rem;
        height: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(250, 225, 215, 0.2);
        border-radius: 0.75rem;
        flex-shrink: 0;
    }

    .progress-indicator {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: var(--cw-gradient-card-subtle);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
    }

    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(var(--cw-brand-primary) calc(var(--progress, 0) * 1%), var(--cw-neutral-200) 0);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .progress-circle::before {
        content: attr(data-progress) '%';
        position: absolute;
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--cw-brand-primary);
    }

    .progress-text {
        font-weight: 600;
        color: var(--cw-brand-primary);
    }

    /* Back Button */
    .btn-cw-back {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        background: white;
        color: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        margin-bottom: 1.5rem;
    }

    .btn-cw-back:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    /* Main Card */
    .faqs-card {
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-neutral-200);
        overflow: hidden;
    }

    /* Category Tabs */
    .faq-categories {
        display: flex;
        background: var(--cw-gradient-card-subtle);
        border-bottom: 2px solid var(--cw-brand-accent);
        overflow-x: auto;
    }

    .category-tab {
        flex: 1;
        padding: 1rem 1.5rem;
        border: none;
        background: transparent;
        color: var(--cw-neutral-600);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        min-width: 140px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .category-tab:hover {
        background: rgba(47, 22, 15, 0.05);
        color: var(--cw-brand-primary);
    }

    .category-tab.active {
        background: var(--cw-brand-primary);
        color: white;
    }

    .category-tab.active::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--cw-brand-primary);
    }

    /* Section Styling */
    .section-wrapper {
        padding: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    /* FAQ Templates Grid */
    .templates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .template-card {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .template-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .template-question {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
    }

    .template-answer {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .template-apply-btn {
        width: 100%;
        padding: 0.75rem;
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .template-apply-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    /* FAQ List */
    .faqs-list {
        min-height: 200px;
    }

    .faq-item {
        background: var(--cw-gradient-card-subtle);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .faq-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .faq-item.dragging {
        opacity: 0.7;
        transform: rotate(2deg);
    }

    .faq-header {
        background: white;
        padding: 1.25rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        cursor: move;
    }

    .drag-handle {
        color: var(--cw-neutral-400);
        cursor: grab;
        font-size: 1.25rem;
    }

    .drag-handle:active {
        cursor: grabbing;
    }

    .faq-order {
        background: var(--cw-brand-primary);
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .faq-content {
        flex: 1;
    }

    .faq-question {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .faq-answer {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        line-height: 1.5;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .faq-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .btn-faq-action {
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }

    .btn-cw-secondary {
        background: white;
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-brand-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        text-decoration: none;
    }

    .btn-cw-danger {
        background: #dc2626;
        color: white;
        border: 1px solid #dc2626;
    }

    .btn-cw-danger:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        color: white;
        text-decoration: none;
    }

    /* Empty State */
    .no-faqs-message {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-500);
    }

    .no-faqs-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--cw-neutral-400);
    }

    /* Add FAQ Form */
    .add-faq-section {
        border-top: 2px solid var(--cw-brand-accent);
        padding: 2rem;
        background: var(--cw-gradient-card-subtle);
    }

    .faq-form {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--cw-neutral-200);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    .character-counter {
        font-size: 0.75rem;
        color: var(--cw-neutral-500);
        text-align: right;
        margin-top: 0.25rem;
    }

    .character-counter.warning {
        color: #d97706;
    }

    .character-counter.danger {
        color: #dc2626;
    }

    .help-text {
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        margin-top: 0.5rem;
    }

    /* Form Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }

    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .faqs-container {
            padding: 0 0.5rem;
        }

        .faqs-header,
        .section-wrapper,
        .add-faq-section {
            padding: 1.5rem;
        }

        .faqs-title {
            font-size: 1.75rem;
        }

        .templates-grid {
            grid-template-columns: 1fr;
        }

        .faq-categories {
            flex-direction: column;
        }

        .category-tab {
            min-width: auto;
        }

        .faq-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .faq-actions {
            align-self: flex-start;
        }

        .form-actions {
            flex-direction: column;
        }
    }

    /* Drag and Drop Styling */
    .sortable-ghost {
        opacity: 0.4;
    }

    .sortable-chosen {
        background: var(--cw-brand-accent);
    }

    /* Form Section Styling */
    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #fef7f0 100%);
        border: 1px solid #fae1d7;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-section-title {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 1.25rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        color: #fae1d7;
        margin-right: 0.5rem;
    }

    .form-label {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .form-control {
        border: 1px solid #fae1d7;
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-family: 'Inter', sans-serif;
        color: #525252;
        background-color: #ffffff;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #2F160F;
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.25);
        outline: none;
    }

    .btn-cw-primary {
        background-color: #ffffff;
        border: 2px solid #2F160F;
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-cw-primary:hover {
        background-color: #2F160F;
        color: #ffffff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
    }

    .btn-cw-primary:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .character-count {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
        text-align: right;
    }

    /* FAQ List Styling */
    .faq-item {
        background: #ffffff;
        border: 1px solid #fae1d7;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .faq-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .faq-header {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .drag-handle {
        color: #fae1d7;
        cursor: grab;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: color 0.3s ease;
    }

    .drag-handle:hover {
        color: #2F160F;
    }

    .faq-order {
        background: linear-gradient(135deg, #2F160F 0%, #42241A 100%);
        color: #ffffff;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .faq-content {
        flex: 1;
    }

    .faq-question {
        color: #2F160F;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .faq-answer {
        color: #525252;
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        margin: 0;
    }

    .faq-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .btn-faq-action {
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-cw-secondary {
        background-color: #ffffff;
        border: 1px solid #fae1d7;
        color: #2F160F;
    }

    .btn-cw-secondary:hover {
        background-color: #fef7f0;
        border-color: #2F160F;
        color: #2F160F;
    }

    .btn-cw-danger {
        background-color: #ffffff;
        border: 1px solid #dc3545;
        color: #dc3545;
    }

    .btn-cw-danger:hover {
        background-color: #dc3545;
        color: #ffffff;
    }

    .help-text {
        color: #6c757d;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        margin-top: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Sortable styling */
    .sortable-ghost {
        opacity: 0.5;
        background: #fef7f0;
    }

    .sortable-chosen {
        transform: scale(1.02);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .faq-header {
            flex-direction: column;
            gap: 0.75rem;
        }

        .faq-actions {
            align-self: stretch;
            justify-content: space-between;
        }

        .form-section {
            padding: 1.5rem;
        }
    }

</style>
{% endblock %}

{% block dashboard_content %}
<div class="dashboard-content">

    <!-- Existing FAQs Section -->
    {% if faqs %}
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-question-circle me-2"></i>Current FAQs
        </h4>

        <p class="text-muted mb-3">
            Manage your frequently asked questions. You can have up to {{ max_faqs }} FAQs.
        </p>

        <div id="faqs-list" class="faqs-list">
            {% for faq in faqs %}
            <div class="faq-item" data-faq-id="{{ faq.id }}" data-order="{{ faq.order }}">
                <div class="faq-header">
                    <div class="drag-handle">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                    <div class="faq-order">{{ faq.order }}</div>
                    <div class="faq-content">
                        <div class="faq-question">{{ faq.question }}</div>
                        <p class="faq-answer">{{ faq.answer }}</p>
                    </div>
                    <div class="faq-actions">
                        <a href="{% url 'venues_app:edit_faq' faq.id %}" class="btn-faq-action btn-cw-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{% url 'venues_app:delete_faq' faq.id %}" class="btn-faq-action btn-cw-danger"
                           onclick="return confirm('Are you sure you want to delete this FAQ?')">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="help-text mt-3">
            <i class="fas fa-info-circle me-1"></i>
            {% trans "Drag and drop FAQs to reorder them. Changes are saved automatically." %}
        </div>
    </div>
    {% endif %}

    <!-- Add New FAQ Section -->
    {% if can_add_faq %}
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-plus-circle me-2"></i>Add New FAQ
        </h4>

        <p class="text-muted mb-3">
            Add a new frequently asked question to help customers understand your services better.
        </p>

        <form method="post" id="faq-form" novalidate>
            {% csrf_token %}

            <div class="row">
                <div class="col-12 mb-3">
                    <label for="{{ form.question.id_for_label }}" class="form-label fw-bold">
                        Question <span class="text-danger">*</span>
                    </label>
                    {{ form.question }}
                    {% if form.question.errors %}
                        {% for error in form.question.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="character-count">
                        <span id="question-count">0</span>/255 characters
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mb-3">
                    <label for="{{ form.answer.id_for_label }}" class="form-label fw-bold">
                        Answer <span class="text-danger">*</span>
                    </label>
                    {{ form.answer }}
                    {% if form.answer.errors %}
                        {% for error in form.answer.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="character-count">
                        <span id="answer-count">0</span>/500 characters
                    </div>
                    <small class="form-text text-muted">
                        Provide a clear and helpful answer to this question.
                    </small>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-cw-primary" id="submit-btn">
                    <i class="fas fa-plus me-2"></i>Add FAQ
                </button>
            </div>
        </form>
    </div>
    {% else %}
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-info-circle me-2"></i>Maximum FAQs Reached
        </h4>
        <p class="text-muted">
            You have reached the maximum number of FAQs ({{ max_faqs }}) for your venue. Please delete an existing FAQ to add a new one.
        </p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize drag and drop for FAQ reordering
    const faqsList = document.getElementById('faqs-list');
    if (faqsList) {
        new Sortable(faqsList, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                // Get new order
                const items = Array.from(faqsList.children);
                const newOrder = items.map((item, index) => ({
                    id: item.getAttribute('data-faq-id'),
                    order: index + 1
                }));

                // Send AJAX request to update order
                fetch('{% url "venues_app:reorder_faqs" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        faqs: newOrder
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update order numbers in UI
                        items.forEach((item, index) => {
                            const orderElement = item.querySelector('.faq-order');
                            if (orderElement) {
                                orderElement.textContent = index + 1;
                            }
                            item.setAttribute('data-order', index + 1);
                        });
                    } else {
                        console.error('Failed to reorder FAQs:', data.error);
                        // Revert the change
                        location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    location.reload();
                });
            }
        });
    }

    // Character counting
    function updateCharacterCount(input, countElement, maxLength) {
        const count = input.value.length;
        countElement.textContent = count;

        if (count > maxLength * 0.9) {
            countElement.style.color = '#dc3545';
        } else if (count > maxLength * 0.7) {
            countElement.style.color = '#ffc107';
        } else {
            countElement.style.color = '#6c757d';
        }
    }

    // Question character count
    const questionInput = document.getElementById('{{ form.question.id_for_label }}');
    const questionCount = document.getElementById('question-count');
    if (questionInput && questionCount) {
        updateCharacterCount(questionInput, questionCount, 255);
        questionInput.addEventListener('input', function() {
            updateCharacterCount(this, questionCount, 255);
        });
    }

    // Answer character count
    const answerInput = document.getElementById('{{ form.answer.id_for_label }}');
    const answerCount = document.getElementById('answer-count');
    if (answerInput && answerCount) {
        updateCharacterCount(answerInput, answerCount, 500);
        answerInput.addEventListener('input', function() {
            updateCharacterCount(this, answerCount, 500);
        });
    }

    // Form validation
    const form = document.getElementById('faq-form');
    const submitBtn = document.getElementById('submit-btn');

    if (form) {
        form.addEventListener('input', validateForm);

        function validateForm() {
            const question = form.querySelector('[name="question"]').value.trim();
            const answer = form.querySelector('[name="answer"]').value.trim();

            const isValid = question.length >= 10 && answer.length >= 20;
            submitBtn.disabled = !isValid;
        }

        validateForm(); // Initial validation
    }
});
</script>
{% endblock %}
