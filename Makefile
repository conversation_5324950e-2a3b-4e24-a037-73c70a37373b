.PHONY: run reset_db seed_data test_coverage pytest pytest_app pytest_coverage pytest_fast



# --- Start Django development server (accessible via LAN) ---
run:
	@echo "🚀 Starting Django development server at http://0.0.0.0:8000 ..."
	python manage.py runserver 0.0.0.0:8000



# --- Reset local SQLite database and clean cache files ---
reset_db:
	@echo "🔄  Starting full Django reset: database, cache, and media files..."
	@echo "🗑️  Deleting SQLite database file..."
	rm -f db.sqlite3 || true

	@echo "🧹  Cleaning migration cache files (*.pyc)..."
	find . -path "*/migrations/*.pyc" -delete || true

	@echo "🗑️  Removing all Python __pycache__ folders..."
	find . -name "__pycache__" -type d -exec rm -rf {} + || true

	@echo "🗑️  Deleting Python bytecode files (*.pyc, *.pyo)..."
	find . -name "*.pyc" -delete || true
	find . -name "*.pyo" -delete || true

	@echo "🖼️  Clearing all user-uploaded media files (media/*)..."
	rm -rf media/* || true

	@echo "⚙️  Applying migrations to rebuild the database schema..."
	python manage.py migrate

	@echo "🏙️  Seeding US cities data from CSV file..."
	python manage.py seed_us_cities --clear

	@echo "👤  Creating Django superuser (Email: <EMAIL>, Password: 123)..."
	python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); \
email='<EMAIL>'; password='123'; \
User.objects.create_superuser(email=email, password=password); \
print('Superuser created successfully!')"

	@echo "✅  Reset complete! Fresh database, no cached files, superuser created, and US cities seeded."



# --- Seed database with test data ---
seed_data:
	@echo "🌱 Seeding database with test data..."
	python manage.py seed_data
	@echo "🏷️ Seeding service categories..."
	python manage.py seed_service_categories
	@echo "✅ Database seeding completed!"



# --- Run Django tests with coverage reporting ---
test_coverage:
	@echo "📊 [1/1] Running Django tests with coverage..."
	coverage run --source='.' manage.py test
	@echo "📝 Coverage report:"
	coverage report
	@echo "✅ All tests with coverage completed!"



# ---Run all tests using pytest ---
pytest:
	@echo "🧪 [1/1] Running all tests with pytest..."
	pytest
	@echo "✅ All pytest tests completed!"



# --- Run tests for a specific app using pytest, Usage: make pytest_app APP=app_name ---
pytest_app:
	@echo "🧪 [1/2] Running tests for specific app with pytest..."
	@echo "ℹ️  Usage: make pytest_app APP=accounts_app"
	@if [ -z "$(APP)" ]; then \
		echo "❌ Please specify APP=<app_name> (e.g., make pytest_app APP=accounts_app)"; \
		exit 1; \
	fi
	pytest $(APP)/tests/ -v
	@echo "✅ Tests for app [$(APP)] completed!"



# --- Run pytest with coverage, optionally for a specific app, Usage: make pytest_coverage [APP=app_name] ---
pytest_coverage:
	@echo "📊 [1/2] Running pytest with coverage..."
	@if [ -z "$(APP)" ]; then \
		echo "🗂️  Running coverage for ALL apps..."; \
		pytest --cov=. --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report generated at htmlcov/index.html"; \
	else \
		echo "🗂️  Running coverage for app: [$(APP)] ..."; \
		pytest $(APP)/tests/ --cov=$(APP) --cov-report=term-missing --cov-report=html; \
		echo "📄 HTML coverage report for $(APP) at htmlcov/index.html"; \
	fi
	@echo "✅ Pytest with coverage completed!"



# --- Run pytest quickly with minimal output (quiet mode) ---
pytest_fast:
	@echo "🏃 [1/1] Running pytest in fast mode (quiet output)..."
	pytest -q --tb=no
	@echo "✅ Fast pytest run completed!"








